'use client';

import React, { useState } from 'react';
import { SurveyResultsChart } from './SurveyResultsChart';

// Test data scenarios based on the provided backend response
const testScenarios = {
  standard: {
    name: "Standard 5-Category (Original Data)",
    conceptStatements: [
      {
        labels: [
          "Strongly Disagree",
          "Disagree", 
          "Neutral",
          "Agree",
          "Strongly Agree"
        ],
        statement: "Would taste great"
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral", 
          "Agree",
          "Strongly Agree"
        ],
        statement: "Would have good texture"
      }
    ],
    surveyResults: [
      {
        avg_score: 3.83,
        net_sentiment: 58,
        responses: {
          "Agree": 41,
          "Disagree": 8,
          "Neutral": 18,
          "Strongly Agree": 29,
          "Strongly Disagree": 4
        },
        statement: "Would taste great"
      },
      {
        avg_score: 3.59,
        net_sentiment: 41,
        responses: {
          "Agree": 36,
          "Disagree": 13,
          "Neutral": 23,
          "Strongly Agree": 23,
          "Strongly Disagree": 5
        },
        statement: "Would have good texture"
      }
    ]
  },
  
  threeCategory: {
    name: "3-Category Scale",
    conceptStatements: [
      {
        labels: ["Poor", "Good", "Excellent"],
        statement: "Overall quality rating"
      },
      {
        labels: ["Poor", "Good", "Excellent"],
        statement: "Value for money"
      }
    ],
    surveyResults: [
      {
        avg_score: 2.1,
        net_sentiment: 15,
        responses: {
          "Poor": 45,
          "Good": 35,
          "Excellent": 20
        },
        statement: "Overall quality rating"
      },
      {
        avg_score: 1.8,
        net_sentiment: -10,
        responses: {
          "Poor": 55,
          "Good": 30,
          "Excellent": 15
        },
        statement: "Value for money"
      }
    ]
  },

  sevenCategory: {
    name: "7-Category Scale (NPS-style)",
    conceptStatements: [
      {
        labels: [
          "Extremely Unlikely",
          "Very Unlikely", 
          "Unlikely",
          "Neutral",
          "Likely",
          "Very Likely",
          "Extremely Likely"
        ],
        statement: "Likelihood to purchase"
      }
    ],
    surveyResults: [
      {
        avg_score: 4.2,
        net_sentiment: 35,
        responses: {
          "Extremely Unlikely": 5,
          "Very Unlikely": 8,
          "Unlikely": 12,
          "Neutral": 25,
          "Likely": 30,
          "Very Likely": 15,
          "Extremely Likely": 5
        },
        statement: "Likelihood to purchase"
      }
    ]
  },

  smallPercentages: {
    name: "Small Percentages Test (Visibility)",
    conceptStatements: [
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral", 
          "Agree",
          "Strongly Agree"
        ],
        statement: "Would recommend to others"
      }
    ],
    surveyResults: [
      {
        avg_score: 4.1,
        net_sentiment: 75,
        responses: {
          "Strongly Disagree": 1, // 1% - should test visibility
          "Disagree": 2,          // 2% - should test visibility  
          "Neutral": 7,           // 7% - should test visibility
          "Agree": 40,            // 40% - should be visible
          "Strongly Agree": 50    // 50% - should be visible
        },
        statement: "Would recommend to others"
      }
    ]
  },

  fourCategory: {
    name: "4-Category Scale",
    conceptStatements: [
      {
        labels: ["Not at all", "Slightly", "Moderately", "Extremely"],
        statement: "How appealing is this product?"
      },
      {
        labels: ["Not at all", "Slightly", "Moderately", "Extremely"],
        statement: "How likely are you to try this?"
      }
    ],
    surveyResults: [
      {
        avg_score: 2.8,
        net_sentiment: 25,
        responses: {
          "Not at all": 15,
          "Slightly": 25,
          "Moderately": 35,
          "Extremely": 25
        },
        statement: "How appealing is this product?"
      },
      {
        avg_score: 2.5,
        net_sentiment: 10,
        responses: {
          "Not at all": 20,
          "Slightly": 30,
          "Moderately": 30,
          "Extremely": 20
        },
        statement: "How likely are you to try this?"
      }
    ]
  }
};

export const SurveyResultsChartTest: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<keyof typeof testScenarios>('standard');
  
  const currentData = testScenarios[selectedScenario];

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            SurveyResultsChart Test Suite
          </h1>
          <p className="text-gray-600 mb-6">
            Test the chart component with different category labels and data scenarios.
            Pay special attention to small percentages visibility and legend formatting.
          </p>
          
          {/* Scenario Selector */}
          <div className="flex flex-wrap gap-2 mb-6">
            {Object.entries(testScenarios).map(([key, scenario]) => (
              <button
                key={key}
                onClick={() => setSelectedScenario(key as keyof typeof testScenarios)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedScenario === key
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {scenario.name}
              </button>
            ))}
          </div>

          {/* Current Scenario Info */}
          <div className="bg-white rounded-lg p-4 mb-6 border">
            <h3 className="font-semibold text-gray-900 mb-2">Current Test: {currentData.name}</h3>
            <div className="text-sm text-gray-600">
              <p><strong>Categories:</strong> {currentData.conceptStatements[0]?.labels.join(', ')}</p>
              <p><strong>Statements:</strong> {currentData.surveyResults.length}</p>
              <p><strong>Total Responses:</strong> {
                currentData.surveyResults[0] ? 
                Object.values(currentData.surveyResults[0].responses).reduce((sum, val) => sum + val, 0) 
                : 0
              }</p>
            </div>
          </div>
        </div>

        {/* Chart Component */}
        <SurveyResultsChart 
          data={currentData.surveyResults}
          conceptStatements={currentData.conceptStatements}
        />

        {/* Debug Info */}
        <div className="mt-8 bg-gray-100 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-2">Debug Information</h3>
          <div className="text-xs text-gray-600">
            <p><strong>Test Focus:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Small percentages (1-7%) should have minimum width for visibility</li>
              <li>Percentages ≥8% should display percentage text inside bars</li>
              <li>Legend should show indexed numbers [1], [2], etc.</li>
              <li>Colors should adapt to different numbers of categories</li>
              <li>Average scores should display with 2 decimal places</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
