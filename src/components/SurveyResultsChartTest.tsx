"use client";

import React, { useState } from "react";
import { SurveyResultsChart } from "./SurveyResultsChart";

// Test data scenarios based on the provided backend response
const testScenarios = {
  standard: {
    name: "Standard 5-Category (Original Data)",
    conceptStatements: [
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would taste great",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would have good texture",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would be healthy",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would be worth the price",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would recommend to friends",
      },
    ],
    surveyResults: [
      {
        avg_score: 3.83,
        net_sentiment: 58,
        responses: {
          Agree: 41,
          Disagree: 8,
          Neutral: 18,
          "Strongly Agree": 29,
          "Strongly Disagree": 4,
        },
        statement: "Would taste great",
      },
      {
        avg_score: 3.59,
        net_sentiment: 41,
        responses: {
          Agree: 36,
          Disagree: 13,
          Neutral: 23,
          "Strongly Agree": 23,
          "Strongly Disagree": 5,
        },
        statement: "Would have good texture",
      },
      {
        avg_score: 4.12,
        net_sentiment: 72,
        responses: {
          Agree: 38,
          Disagree: 6,
          Neutral: 12,
          "Strongly Agree": 42,
          "Strongly Disagree": 2,
        },
        statement: "Would be healthy",
      },
      {
        avg_score: 2.95,
        net_sentiment: 15,
        responses: {
          Agree: 28,
          Disagree: 22,
          Neutral: 25,
          "Strongly Agree": 15,
          "Strongly Disagree": 10,
        },
        statement: "Would be worth the price",
      },
      {
        avg_score: 3.45,
        net_sentiment: 35,
        responses: {
          Agree: 32,
          Disagree: 15,
          Neutral: 20,
          "Strongly Agree": 25,
          "Strongly Disagree": 8,
        },
        statement: "Would recommend to friends",
      },
    ],
  },

  threeCategory: {
    name: "3-Category Scale",
    conceptStatements: [
      {
        labels: ["Poor", "Good", "Excellent"],
        statement: "Overall quality rating",
      },
      {
        labels: ["Poor", "Good", "Excellent"],
        statement: "Value for money",
      },
      {
        labels: ["Poor", "Good", "Excellent"],
        statement: "Brand reputation",
      },
      {
        labels: ["Poor", "Good", "Excellent"],
        statement: "Customer service",
      },
    ],
    surveyResults: [
      {
        avg_score: 2.1,
        net_sentiment: 15,
        responses: {
          Poor: 45,
          Good: 35,
          Excellent: 20,
        },
        statement: "Overall quality rating",
      },
      {
        avg_score: 1.8,
        net_sentiment: -10,
        responses: {
          Poor: 55,
          Good: 30,
          Excellent: 15,
        },
        statement: "Value for money",
      },
      {
        avg_score: 2.4,
        net_sentiment: 25,
        responses: {
          Poor: 30,
          Good: 50,
          Excellent: 20,
        },
        statement: "Brand reputation",
      },
      {
        avg_score: 2.2,
        net_sentiment: 20,
        responses: {
          Poor: 40,
          Good: 40,
          Excellent: 20,
        },
        statement: "Customer service",
      },
    ],
  },

  sevenCategory: {
    name: "7-Category Scale (NPS-style)",
    conceptStatements: [
      {
        labels: [
          "Extremely Unlikely",
          "Very Unlikely",
          "Unlikely",
          "Neutral",
          "Likely",
          "Very Likely",
          "Extremely Likely",
        ],
        statement: "Likelihood to purchase",
      },
      {
        labels: [
          "Extremely Unlikely",
          "Very Unlikely",
          "Unlikely",
          "Neutral",
          "Likely",
          "Very Likely",
          "Extremely Likely",
        ],
        statement: "Likelihood to recommend",
      },
      {
        labels: [
          "Extremely Unlikely",
          "Very Unlikely",
          "Unlikely",
          "Neutral",
          "Likely",
          "Very Likely",
          "Extremely Likely",
        ],
        statement: "Likelihood to try again",
      },
    ],
    surveyResults: [
      {
        avg_score: 4.2,
        net_sentiment: 35,
        responses: {
          "Extremely Unlikely": 5,
          "Very Unlikely": 8,
          Unlikely: 12,
          Neutral: 25,
          Likely: 30,
          "Very Likely": 15,
          "Extremely Likely": 5,
        },
        statement: "Likelihood to purchase",
      },
      {
        avg_score: 3.8,
        net_sentiment: 25,
        responses: {
          "Extremely Unlikely": 8,
          "Very Unlikely": 12,
          Unlikely: 15,
          Neutral: 30,
          Likely: 20,
          "Very Likely": 10,
          "Extremely Likely": 5,
        },
        statement: "Likelihood to recommend",
      },
      {
        avg_score: 4.5,
        net_sentiment: 45,
        responses: {
          "Extremely Unlikely": 3,
          "Very Unlikely": 5,
          Unlikely: 8,
          Neutral: 20,
          Likely: 35,
          "Very Likely": 20,
          "Extremely Likely": 9,
        },
        statement: "Likelihood to try again",
      },
    ],
  },

  smallPercentages: {
    name: "Small Percentages Test (Visibility)",
    conceptStatements: [
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would recommend to others",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Would buy again",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "Meets expectations",
      },
    ],
    surveyResults: [
      {
        avg_score: 4.1,
        net_sentiment: 75,
        responses: {
          "Strongly Disagree": 1, // 1% - should test visibility
          Disagree: 2, // 2% - should test visibility
          Neutral: 7, // 7% - should test visibility
          Agree: 40, // 40% - should be visible
          "Strongly Agree": 50, // 50% - should be visible
        },
        statement: "Would recommend to others",
      },
      {
        avg_score: 3.9,
        net_sentiment: 65,
        responses: {
          "Strongly Disagree": 2, // 2% - should test visibility
          Disagree: 3, // 3% - should test visibility
          Neutral: 10, // 10% - should be visible
          Agree: 45, // 45% - should be visible
          "Strongly Agree": 40, // 40% - should be visible
        },
        statement: "Would buy again",
      },
      {
        avg_score: 4.2,
        net_sentiment: 80,
        responses: {
          "Strongly Disagree": 1, // 1% - should test visibility
          Disagree: 1, // 1% - should test visibility
          Neutral: 5, // 5% - should test visibility
          Agree: 35, // 35% - should be visible
          "Strongly Agree": 58, // 58% - should be visible
        },
        statement: "Meets expectations",
      },
    ],
  },

  fourCategory: {
    name: "4-Category Scale",
    conceptStatements: [
      {
        labels: ["Not at all", "Slightly", "Moderately", "Extremely"],
        statement: "How appealing is this product?",
      },
      {
        labels: ["Not at all", "Slightly", "Moderately", "Extremely"],
        statement: "How likely are you to try this?",
      },
      {
        labels: ["Not at all", "Slightly", "Moderately", "Extremely"],
        statement: "How innovative does this seem?",
      },
      {
        labels: ["Not at all", "Slightly", "Moderately", "Extremely"],
        statement: "How trustworthy is this brand?",
      },
      {
        labels: ["Not at all", "Slightly", "Moderately", "Extremely"],
        statement: "How convenient is this product?",
      },
    ],
    surveyResults: [
      {
        avg_score: 2.8,
        net_sentiment: 25,
        responses: {
          "Not at all": 15,
          Slightly: 25,
          Moderately: 35,
          Extremely: 25,
        },
        statement: "How appealing is this product?",
      },
      {
        avg_score: 2.5,
        net_sentiment: 10,
        responses: {
          "Not at all": 20,
          Slightly: 30,
          Moderately: 30,
          Extremely: 20,
        },
        statement: "How likely are you to try this?",
      },
      {
        avg_score: 3.1,
        net_sentiment: 40,
        responses: {
          "Not at all": 10,
          Slightly: 20,
          Moderately: 40,
          Extremely: 30,
        },
        statement: "How innovative does this seem?",
      },
      {
        avg_score: 2.7,
        net_sentiment: 20,
        responses: {
          "Not at all": 18,
          Slightly: 27,
          Moderately: 32,
          Extremely: 23,
        },
        statement: "How trustworthy is this brand?",
      },
      {
        avg_score: 3.2,
        net_sentiment: 45,
        responses: {
          "Not at all": 8,
          Slightly: 22,
          Moderately: 35,
          Extremely: 35,
        },
        statement: "How convenient is this product?",
      },
    ],
  },

  manyStatements: {
    name: "Many Statements Test (8 statements)",
    conceptStatements: [
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "This product looks appealing",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "This product seems high quality",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "This product is worth the price",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "I would purchase this product",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "I would recommend this to friends",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "This product meets my needs",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "This brand is trustworthy",
      },
      {
        labels: [
          "Strongly Disagree",
          "Disagree",
          "Neutral",
          "Agree",
          "Strongly Agree",
        ],
        statement: "This product is innovative",
      },
    ],
    surveyResults: [
      {
        avg_score: 3.2,
        net_sentiment: 30,
        responses: {
          "Strongly Disagree": 8,
          Disagree: 12,
          Neutral: 25,
          Agree: 35,
          "Strongly Agree": 20,
        },
        statement: "This product looks appealing",
      },
      {
        avg_score: 3.5,
        net_sentiment: 45,
        responses: {
          "Strongly Disagree": 5,
          Disagree: 10,
          Neutral: 20,
          Agree: 40,
          "Strongly Agree": 25,
        },
        statement: "This product seems high quality",
      },
      {
        avg_score: 2.8,
        net_sentiment: 10,
        responses: {
          "Strongly Disagree": 15,
          Disagree: 20,
          Neutral: 30,
          Agree: 25,
          "Strongly Agree": 10,
        },
        statement: "This product is worth the price",
      },
      {
        avg_score: 3.1,
        net_sentiment: 25,
        responses: {
          "Strongly Disagree": 10,
          Disagree: 15,
          Neutral: 25,
          Agree: 30,
          "Strongly Agree": 20,
        },
        statement: "I would purchase this product",
      },
      {
        avg_score: 3.3,
        net_sentiment: 35,
        responses: {
          "Strongly Disagree": 7,
          Disagree: 13,
          Neutral: 22,
          Agree: 33,
          "Strongly Agree": 25,
        },
        statement: "I would recommend this to friends",
      },
      {
        avg_score: 3.4,
        net_sentiment: 40,
        responses: {
          "Strongly Disagree": 6,
          Disagree: 12,
          Neutral: 20,
          Agree: 37,
          "Strongly Agree": 25,
        },
        statement: "This product meets my needs",
      },
      {
        avg_score: 3.0,
        net_sentiment: 20,
        responses: {
          "Strongly Disagree": 12,
          Disagree: 18,
          Neutral: 25,
          Agree: 28,
          "Strongly Agree": 17,
        },
        statement: "This brand is trustworthy",
      },
      {
        avg_score: 3.6,
        net_sentiment: 50,
        responses: {
          "Strongly Disagree": 4,
          Disagree: 8,
          Neutral: 18,
          Agree: 42,
          "Strongly Agree": 28,
        },
        statement: "This product is innovative",
      },
    ],
  },
};

export const SurveyResultsChartTest: React.FC = () => {
  const [selectedScenario, setSelectedScenario] =
    useState<keyof typeof testScenarios>("standard");

  const currentData = testScenarios[selectedScenario];

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            SurveyResultsChart Test Suite
          </h1>
          <p className="text-gray-600 mb-6">
            Test the chart component with different category labels and data
            scenarios. Pay special attention to small percentages visibility and
            legend formatting.
          </p>

          {/* Scenario Selector */}
          <div className="flex flex-wrap gap-2 mb-6">
            {Object.entries(testScenarios).map(([key, scenario]) => (
              <button
                key={key}
                onClick={() =>
                  setSelectedScenario(key as keyof typeof testScenarios)
                }
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedScenario === key
                    ? "bg-blue-600 text-white"
                    : "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                }`}
              >
                {scenario.name}
              </button>
            ))}
          </div>

          {/* Current Scenario Info */}
          <div className="bg-white rounded-lg p-4 mb-6 border">
            <h3 className="font-semibold text-gray-900 mb-2">
              Current Test: {currentData.name}
            </h3>
            <div className="text-sm text-gray-600">
              <p>
                <strong>Categories:</strong>{" "}
                {currentData.conceptStatements[0]?.labels.join(", ")}
              </p>
              <p>
                <strong>Number of Categories:</strong>{" "}
                {currentData.conceptStatements[0]?.labels.length || 0}
              </p>
              <p>
                <strong>Number of Statements:</strong>{" "}
                {currentData.surveyResults.length}
              </p>
              <p>
                <strong>Total Responses per Statement:</strong>{" "}
                {currentData.surveyResults[0]
                  ? Object.values(
                      currentData.surveyResults[0].responses
                    ).reduce((sum, val) => sum + val, 0)
                  : 0}
              </p>
            </div>
          </div>
        </div>

        {/* Chart Component */}
        <SurveyResultsChart
          data={currentData.surveyResults}
          conceptStatements={currentData.conceptStatements}
        />

        {/* Debug Info */}
        <div className="mt-8 bg-gray-100 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-2">
            Debug Information
          </h3>
          <div className="text-xs text-gray-600">
            <p>
              <strong>Test Focus:</strong>
            </p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>
                Small percentages (1-7%) should have minimum width for
                visibility
              </li>
              <li>
                Percentages ≥8% should display percentage text inside bars
              </li>
              <li>Legend should show indexed numbers [1], [2], etc.</li>
              <li>Colors should adapt to different numbers of categories</li>
              <li>Average scores should display with 2 decimal places</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
