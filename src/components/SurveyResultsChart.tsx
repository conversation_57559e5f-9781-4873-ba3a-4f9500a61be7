import React from "react";

interface SurveyResult {
  statement: string;
  responses: Record<string, number>;
  net_sentiment: number;
  avg_score: number;
}

interface ConceptStatement {
  labels: string[];
  statement: string;
}

interface SurveyResultsChartProps {
  data: SurveyResult[];
  conceptStatements: ConceptStatement[];
}

export const SurveyResultsChart: React.FC<SurveyResultsChartProps> = ({
  data,
  conceptStatements,
}) => {
  const getPercentage = (value: number, total: number) => {
    return (value / total) * 100;
  };

  const categoryLabels =
    conceptStatements.length > 0 ? conceptStatements[0].labels : [];

  const generateColors = (numCategories: number): string[] => {
    const baseColors = [
      "#EF4444", // red-500 (most negative)
      "#FB923C", // orange-400
      "#D1D5DB", // gray-300 (neutral)
      "#10B981", // emerald-500 (positive)
      "#34D399", // emerald-400 (most positive)
    ];

    if (numCategories <= baseColors.length) {
      return baseColors.slice(0, numCategories);
    }

    // If more categories than base colors, generate additional colors
    const additionalColors = [];
    for (let i = baseColors.length; i < numCategories; i++) {
      additionalColors.push(`hsl(${(i * 360) / numCategories}, 70%, 50%)`);
    }
    return [...baseColors, ...additionalColors];
  };

  const colors = generateColors(categoryLabels.length);
  const colorMap = categoryLabels.reduce(
    (acc, label, index) => {
      acc[label] = colors[index];
      return acc;
    },
    {} as Record<string, string>
  );

  // Create legend with indexed numbers
  const legend = categoryLabels.map((label, index) => ({
    label: `[${index + 1}] ${label}`,
    color: colorMap[label],
  }));

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 lg:p-8">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 text-center mb-6">
          SURVEY RESULTS
        </h2>

        <div className="space-y-6">
          {/* X-axis percentage labels - aligned with chart bars */}
          <div className="hidden md:block">
            <div className="flex items-center gap-4">
              <div className="flex-1 flex justify-between text-sm text-gray-600">
                <span>0%</span>
                <span>20%</span>
                <span>40%</span>
                <span>60%</span>
                <span>80%</span>
                <span>100%</span>
              </div>
              <div className="flex-shrink-0 w-20"></div>{" "}
            </div>
          </div>

          {data.map((item, index) => {
            const total = Object.values(item.responses).reduce(
              (sum, val) => sum + val,
              0
            );

            return (
              <div key={index} className="space-y-3 mb-8">
                {/* Statement Header */}
                <div className="text-sm md:text-base text-gray-700 font-medium leading-tight">
                  {item.statement}
                </div>

                {/* Chart Container with proper alignment */}
                <div className="flex items-center gap-4">
                  {/* Stacked bar container */}
                  <div className="flex-1 relative">
                    {/* Small percentage labels container - positioned above the bar */}
                    <div className="absolute -top-6 left-0 right-0 flex h-5">
                      {categoryLabels.map((category) => {
                        const value = item.responses[category] || 0;
                        const percentage = getPercentage(value, total);

                        return (
                          <div
                            key={`label-${category}`}
                            className="flex items-center justify-center"
                            style={{
                              width: `${percentage}%`,
                              minWidth: percentage > 0 ? "8px" : "0px",
                            }}
                          >
                            {percentage > 0 && percentage < 8 ? (
                              <span
                                className="text-gray-700 text-xs font-medium whitespace-nowrap"
                                style={{ fontSize: "10px" }}
                              >
                                {Math.round(percentage)}%
                              </span>
                            ) : null}
                          </div>
                        );
                      })}
                    </div>

                    {/* Main stacked bar */}
                    <div className="flex h-10 md:h-12 bg-gray-200 rounded overflow-hidden">
                      {categoryLabels.map((category) => {
                        const value = item.responses[category] || 0;
                        const percentage = getPercentage(value, total);

                        return (
                          <div
                            key={category}
                            className="flex items-center justify-center text-white text-xs font-medium transition-all duration-200 hover:opacity-90"
                            style={{
                              width: `${percentage}%`,
                              backgroundColor: colorMap[category],
                              minWidth: percentage > 0 ? "8px" : "0px",
                            }}
                          >
                            {percentage >= 8
                              ? `${Math.round(percentage)}%`
                              : ""}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  <div className="flex-shrink-0">
                    <span className="bg-gray-800 text-white text-xs px-2 py-1 rounded font-medium">
                      Avg: {item.avg_score.toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Mobile percentage labels */}
                <div className="flex md:hidden justify-between text-xs text-gray-500 ml-0">
                  <span>0%</span>
                  <span>50%</span>
                  <span>100%</span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Legend */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex flex-wrap justify-center gap-3 md:gap-6">
            {legend.map((item) => (
              <div key={item.label} className="flex items-center gap-2 min-w-0">
                <div
                  className="w-3 h-3 md:w-4 md:h-4 rounded flex-shrink-0"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-xs md:text-sm text-gray-700 truncate">
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
